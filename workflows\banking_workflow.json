{"workflow": {"id": "banking_customer_service", "name": "Banking Customer Service", "version": "1.0", "start": "Greeting", "allowed_actions": ["Check account balance", "Transfer funds", "Apply for loan", "Report lost card", "Update personal information"], "prohibited_actions": ["Do not share PINs or passwords", "Do not process transactions without verification", "Do not disclose sensitive account details"], "interrupt_config": {"global_settings": {"enabled": true, "vad_threshold": 0.01, "confirmation_window_seconds": 0.5, "min_interrupt_duration_seconds": 0.3}}, "states": {"Greeting": {"id": "Greeting", "type": "input", "layer2_id": "l2_greeting_banking_system", "expected_input": [], "expected_output": ["audio_path", "latencyTTS"], "transitions": [{"condition": "intent == 'account_balance'", "target": "CheckBalance"}, {"condition": "intent == 'fund_transfer'", "target": "TransferFunds"}, {"condition": "intent == 'loan_application'", "target": "LoanApplication"}], "allowed_tools": ["STT", "LLM", "TTS", "CACHE"], "interrupt_config": {"reversible": true, "has_side_effect": false, "post_tts_policy": "allow_interrupt", "description": "Greeting messages can be safely interrupted", "interrupt_message": "Please let me finish my greeting, then I'll help you."}}, "CheckBalance": {"id": "CheckBalance", "type": "inform", "layer2_id": "l2_account_balance", "expected_input": ["account_id"], "expected_output": ["account_balance"], "transitions": [{"condition": "true", "target": "OfferMoreHelp"}], "allowed_tools": ["LLM", "TTS", "DB_QUERY"], "interrupt_config": {"reversible": true, "has_side_effect": false, "post_tts_policy": "allow_interrupt", "description": "Balance inquiries are informational and can be safely interrupted", "interrupt_message": "Let me finish telling you your balance, then I'll listen to you."}}, "TransferFunds": {"id": "TransferFunds", "type": "transaction", "layer2_id": "l2_fund_transfer", "expected_input": ["source_account", "target_account", "amount"], "expected_output": ["confirmation_number"], "transitions": [{"condition": "true", "target": "OfferMoreHelp"}], "allowed_tools": ["LLM", "TTS", "TRANSACTION_API"], "interrupt_config": {"reversible": false, "has_side_effect": true, "post_tts_policy": "complete_and_explain", "description": "Fund transfers are irreversible financial transactions", "interrupt_message": "This transfer cannot be undone. Please let me finish processing it before you speak."}}, "LoanApplication": {"id": "LoanApplication", "type": "process", "layer2_id": "l2_loan_application", "expected_input": ["income", "credit_score", "requested_amount"], "expected_output": ["loan_approval_status", "interest_rate"], "transitions": [{"condition": "loan_approval_status == 'approved'", "target": "LoanApproved"}, {"condition": "loan_approval_status == 'denied'", "target": "LoanDenied"}], "allowed_tools": ["LLM", "TTS", "CREDIT_CHECK"], "interrupt_config": {"reversible": true, "has_side_effect": false, "post_tts_policy": "allow_interrupt", "description": "Loan applications have complex processing - reversibility depends on stage", "interrupt_message": "Let me finish your loan application, then I'll respond to your request."}}, "LoanApproved": {"id": "LoanApproved", "type": "inform", "layer2_id": "l2_loan_approved", "expected_input": [], "expected_output": ["next_steps"], "transitions": [{"condition": "true", "target": "OfferMoreHelp"}], "allowed_tools": ["LLM", "TTS"], "interrupt_config": {"reversible": true, "has_side_effect": false, "post_tts_policy": "allow_interrupt", "description": "Loan approval notifications are informational and can be safely interrupted", "interrupt_message": "Let me finish telling you about your loan approval, then I'll listen to you."}}, "LoanDenied": {"id": "LoanDenied", "type": "inform", "layer2_id": "l2_loan_denied", "expected_input": [], "expected_output": ["denial_reason"], "transitions": [{"condition": "true", "target": "OfferMoreHelp"}], "allowed_tools": ["LLM", "TTS"], "interrupt_config": {"reversible": true, "has_side_effect": false, "post_tts_policy": "allow_interrupt", "description": "Loan denial notifications are informational and can be safely interrupted", "interrupt_message": "Let me finish explaining your loan denial, then I'll listen to you."}}, "OfferMoreHelp": {"id": "OfferMoreHelp", "type": "decision", "layer2_id": "l2_more_help", "expected_input": [], "expected_output": ["user_response"], "transitions": [{"condition": "user_response == 'no'", "target": "Goodbye"}], "allowed_tools": ["STT", "LLM", "TTS"], "interrupt_config": {"reversible": true, "has_side_effect": false, "post_tts_policy": "allow_interrupt", "description": "Help offers are conversational and can be safely interrupted", "interrupt_message": "Let me finish offering help, then I'll listen to your next request."}}, "Goodbye": {"id": "Goodbye", "type": "end", "layer2_id": "l2_goodbye_banking_system", "expected_input": [], "expected_output": ["exit_signal"], "transitions": [], "allowed_tools": ["TTS"], "interrupt_config": {"reversible": true, "has_side_effect": false, "post_tts_policy": "allow_interrupt", "description": "Goodbye messages can be safely interrupted", "interrupt_message": "Let me finish saying goodbye, then I'll listen to you."}}}}}