"""
Action Reversibility Detection and Management

This module provides utilities for detecting whether actions are reversible
and generating appropriate user messaging for interrupt scenarios.
"""

from typing import Dict, Any, Optional, List
from enum import Enum
from dataclasses import dataclass
from datetime import datetime

from core.logging.logger_config import get_module_logger

logger = get_module_logger("action_reversibility")


# class ActionType(Enum):
#     """Types of actions that can be performed."""
#     FINANCIAL_TRANSFER = "financial_transfer"
#     DATA_MODIFICATION = "data_modification"
#     COMMUNICATION = "communication"
#     QUERY = "query"
#     CONFIGURATION = "configuration"
#     AUTHENTICATION = "authentication"
#     UNKNOWN = "unknown"


class ReversibilityLevel(Enum):
    """Levels of action reversibility."""
    FULLY_REVERSIBLE = "fully_reversible"      # Can be undone completely
    IRREVERSIBLE = "irreversible"              # Cannot be undone
    UNKNOWN = "unknown"                        # Reversibility unknown


@dataclass
class ActionMetadata:
    """Metadata about an action's characteristics."""
    reversibility: ReversibilityLevel
    requires_confirmation: bool = False
    side_effect_severity: str = "low"  # low, medium, high, critical
    undo_method: Optional[str] = None
    confirmation_message: Optional[str] = None
    interrupt_message: Optional[str] = None


class ActionReversibilityDetector:
    """
    Detects action reversibility based on explicit config fields in context.
    Pattern-based inference is disabled; only explicit fields are used.
    """
    
    def __init__(self):
        self.logger = logger
        # Define contextually appropriate interrupt messages
        self.interrupt_messages = {
            ReversibilityLevel.FULLY_REVERSIBLE:
                "Allow me to finish this first, then I’ll respond to what you said.",
            ReversibilityLevel.IRREVERSIBLE:
                "The action has already been completed. If something went wrong, let me know and I’ll help fix it.",
            ReversibilityLevel.UNKNOWN:
                "I understand you want to say something. Allow me to finish this first, then I’ll respond to what you said."
        }

    def get_action_metadata(self, context: Dict[str, Any]) -> ActionMetadata:
        """
        Get action metadata using workflow interrupt config and explicit fields.
        Priority: workflow interrupt_config > explicit fields > defaults
        """
        try:
            print(f"[DEBUG] get_action_metadata context: {context}")
            # Priority 1: Check workflow state interrupt_config
            workflow_state_config = context.get("workflow_state_config", {})
            interrupt_config = workflow_state_config.get("interrupt_config")

            if interrupt_config:
                print(f"[DEBUG] interrupt_config: {interrupt_config}")
                print(f"[DEBUG] type(interrupt_config): {type(interrupt_config)}")
                if hasattr(interrupt_config, 'keys'):
                    print(f"[DEBUG] interrupt_config keys: {list(interrupt_config.keys())}")
                    print("[DEBUG] interrupt_config keys and values:")
                    for k in interrupt_config.keys():
                        print(f"  key: {repr(k)} value: {repr(interrupt_config[k])}")
                else:
                    print(f"[DEBUG] interrupt_config has no keys() method")
                # Use direct key access instead of .get()
                interrupt_message = interrupt_config["interrupt_message"] if "interrupt_message" in interrupt_config else None
                print(f"[DEBUG] interrupt_message found (direct access): {interrupt_message!r}")
                reversible = interrupt_config.get("reversible")
                side_effects = interrupt_config.get("side_effects", False)

                if reversible is not None:
                    reversibility = (
                        ReversibilityLevel.FULLY_REVERSIBLE if reversible
                        else ReversibilityLevel.IRREVERSIBLE
                    )
                else:
                    reversibility = ReversibilityLevel.UNKNOWN

                # Set side effect severity from workflow config
                side_effect_severity = "high" if side_effects else "low"
            else:
                # Priority 2: Use explicit config fields as fallback
                explicit_reversibility = context.get("explicit_reversibility")
                explicit_side_effect = context.get("explicit_side_effect")

                # Set reversibility
                if explicit_reversibility is not None:
                    reversibility = (
                        ReversibilityLevel.FULLY_REVERSIBLE if explicit_reversibility
                        else ReversibilityLevel.IRREVERSIBLE
                    )
                else:
                    reversibility = ReversibilityLevel.UNKNOWN

                # Set side effect severity from explicit config
                if explicit_side_effect is not None:
                    side_effect_severity = "high" if explicit_side_effect else "low"
                else:
                    side_effect_severity = "medium"

                interrupt_message = None

            # Set action type and confirmation (optional, can be extended)
            requires_confirmation = (reversibility == ReversibilityLevel.IRREVERSIBLE)

            # Get interrupt message (check for custom message from workflow config first)
            if not interrupt_message:
                interrupt_message = self.interrupt_messages.get(
                    reversibility,
                    self.interrupt_messages[ReversibilityLevel.UNKNOWN]
                )

            metadata = ActionMetadata(
                reversibility=reversibility,
                requires_confirmation=requires_confirmation,
                side_effect_severity=side_effect_severity,
                interrupt_message=interrupt_message
            )

            self.logger.info(
                "Action metadata generated from workflow interrupt config",
                action="get_action_metadata",
                output_data={
                    "workflow_interrupt_config": interrupt_config is not None,
                    "reversibility": reversibility.value,
                    "side_effect_severity": side_effect_severity,
                    "custom_interrupt_message": interrupt_config.get("interrupt_message") if interrupt_config else None
                },
                layer="action_reversibility"
            )

            return metadata
        except Exception as e:
            self.logger.error(
                "Error getting action metadata (explicit config only)",
                action="get_action_metadata",
                reason=str(e),
                layer="action_reversibility"
            )
            # Return safe defaults
            return ActionMetadata(
                reversibility=ReversibilityLevel.UNKNOWN,
                requires_confirmation=True,
                side_effect_severity="medium",
                interrupt_message=self.interrupt_messages[ReversibilityLevel.UNKNOWN]
            )

    def should_require_confirmation(self, context: Dict[str, Any]) -> bool:
        """
        Determine if an action should require confirmation (explicit config only).
        """
        try:
            metadata = self.get_action_metadata(context)
            return metadata.requires_confirmation
        except Exception as e:
            self.logger.error(
                "Error determining confirmation requirement (explicit config only)",
                action="should_require_confirmation",
                reason=str(e),
                layer="action_reversibility"
            )
            return True

    def generate_interrupt_response(self, context: Dict[str, Any], 
                                  action_already_executed: bool = False) -> str:
        """
        Generate appropriate interrupt response message (explicit config only).
        """
        try:
            metadata = self.get_action_metadata(context)
            if action_already_executed:
                if metadata.reversibility == ReversibilityLevel.IRREVERSIBLE:
                    return self.interrupt_messages[ReversibilityLevel.IRREVERSIBLE]
                else:
                    return "The action has been completed. Let me know if you need any changes."
            else:
                return metadata.interrupt_message
        except Exception as e:
            self.logger.error(
                "Error generating interrupt response (explicit config only)",
                action="generate_interrupt_response",
                reason=str(e),
                layer="action_reversibility"
            )
            return "I understand you want to say something. Let me finish this first."


# Convenience functions
def detect_action_reversibility(context: Dict[str, Any]) -> bool:
    """
    Convenience function to quickly check if an action is reversible.
    
    Args:
        context: Session context
        
    Returns:
        bool: True if action is reversible, False otherwise
    """
    detector = ActionReversibilityDetector()
    metadata = detector.get_action_metadata(context)
    return metadata.reversibility != ReversibilityLevel.IRREVERSIBLE


def get_interrupt_message(context: Dict[str, Any], action_executed: bool = False) -> str:
    """
    Convenience function to get interrupt message.
    
    Args:
        context: Session context
        action_executed: Whether action has been executed
        
    Returns:
        str: Appropriate interrupt message
    """
    detector = ActionReversibilityDetector()
    return detector.generate_interrupt_response(context, action_executed)
